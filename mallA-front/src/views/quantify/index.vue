<template>
  <div class="page-container">
    <div class="content-wrapper">
    
      <!-- 统计卡片 -->
      <el-card class="stats-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>量化数统计</span>
          </div>
        </template>
        
        <div class="stats-rows">
          <!-- Admin统计行 -->
          <div class="stats-row">
            <div class="stats-item">
              <span class="stats-label">Admin的每日累计量化数</span>
              <el-input class="stats-input" placeholder="0.00" v-model="message3" readonly />
            </div>
            <div class="stats-item">
              <span class="stats-label">Admin的累计量化数</span>
              <el-input class="stats-input" placeholder="0.00" v-model="adminTotalQuantity" readonly />
            </div>
          </div>
          
          <div class="stats-divider"></div>
          
          <!-- 中南惠C统计行 -->
          <div class="stats-row">
            <div class="stats-item">
              <span class="stats-label">中南惠C的每日所有ID累计量化数</span>
              <el-input class="stats-input" placeholder="0.00" v-model="message4" readonly />
            </div>
            <div class="stats-item">
              <span class="stats-label">中南惠C的所有ID累计量化数</span>
              <el-input class="stats-input" placeholder="0.00" v-model="cTotalQuantity" readonly />
            </div>
          </div>
          
          <div class="stats-divider"></div>
          
          <!-- 中南惠B统计行 -->
          <div class="stats-row">
            <div class="stats-item">
              <span class="stats-label">中南惠所有B的每日累计量化数</span>
              <el-input class="stats-input" placeholder="0.00" v-model="message5" readonly />
            </div>
            <div class="stats-item">
              <span class="stats-label">中南惠所有B的累计量化数</span>
              <el-input class="stats-input" placeholder="0.00" v-model="bTotalQuantity" readonly />
            </div>
          </div>
          
          <div class="stats-divider"></div>
          
          <!-- 中南惠系统统计行 -->
          <div class="stats-row">
            <div class="stats-item">
              <span class="stats-label">中南惠系统的每日总累计量化数</span>
              <el-input class="stats-input" placeholder="0.00" v-model="message6" readonly />
            </div>
            <div class="stats-item">
              <span class="stats-label">中南惠系统的总累计量化数</span>
              <el-input class="stats-input" placeholder="0.00" v-model="systemTotalQuantity" readonly />
            </div>
          </div>
        </div>
      </el-card>

      <!-- 数据表格卡片 -->
      <el-card class="table-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>数据列表</span>
          </div>
        </template>
        
        <!-- 搜索区域 -->
        <div class="search-form">
          <div class="form-row">
            <div class="form-item">
              <span class="form-label">手机号</span>
              <el-input v-model="searchId" placeholder="请输入手机号" clearable>
                <template #prefix>
                  <el-icon><Phone /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="form-item">
              <span class="form-label">查询日期</span>
              <el-date-picker v-model="startDate" type="date" placeholder="开始日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
            </div>
            <!--<div class="form-item">
              <span class="form-label">结束日期</span>
              <el-date-picker v-model="endDate" type="date" placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
            </div>-->
          </div>
          
          <div class="form-actions">
            <el-button type="primary" @click="fetchData" :loading="loading">
              <el-icon><Search /></el-icon>搜索
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><Refresh /></el-icon>重置
            </el-button>
            <el-button type="success" @click="exportToExcel">
              <el-icon><Download /></el-icon>导出
            </el-button>
          </div>
        </div>

        <!-- 汇总信息 -->
        <div class="summary-section">
          <div class="summary-item">
            <span class="summary-label">今日总量化数:</span>
            <el-input
              class="summary-input"
              :model-value="summaryData.todayTotalQuan || '0.00'"
              readonly
            />
          </div>
          <div class="summary-item">
            <span class="summary-label">今日累计量化数:</span>
            <el-input
              class="summary-input"
              :model-value="summaryData.weightCountTotal || '0.00'"
              readonly
            />
          </div>
        </div>
        <div class="summary-section">
          <div class="summary-item">
            <span class="summary-label">今日总分量:</span>
            <el-input
              class="summary-input"
              :model-value="summaryData.todayTotalWeight || '0.00'"
              readonly
            />
          </div>
          <div class="summary-item">
            <span class="summary-label">今日累计总分量:</span>
            <el-input
              class="summary-input"
              :model-value="summaryData.totalWeight || '0.00'"
              readonly
            />
          </div>
        </div>

        <!-- 表格 -->
        <el-table :data="filteredData" stripe border style="width: 100%">
          <el-table-column prop="updateTime" label="日期" width="120" />
          <el-table-column prop="phone" label="手机号" width="120" />
          <el-table-column prop="username" label="名字（名称）" width="120" />
          <el-table-column prop="quantify" label="今日量化数" />
          <el-table-column prop="Weight" label="今日分量" />
          <el-table-column prop="weightCount" label="累计分量" />
          <el-table-column prop="quantifyCount" label="累计量化数" />
          <template #empty>
            <div style="text-align: center; padding: 20px; color: #999;">暂无数据</div>
          </template>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="fetchData"
            @current-change="fetchData"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { Session } from "../../utils/storage";
import {
  QueryQuantifyCountPages,
  exportQuantifyCountExcel,
  getQuantifyCount,
  saveOrUpdateQuantifyCount
} from "../../api/quantification";
import { Search, Download, Phone, Refresh } from '@element-plus/icons-vue';
import axios from "axios";

const radio10 = ref("1");
const message1 = ref("");
const message3 = ref("");
const message4 = ref("");
const message5 = ref("");
const message6 = ref("");

const adminTotalQuantity = ref("");
const cTotalQuantity = ref("");
const bTotalQuantity = ref("");
const systemTotalQuantity = ref("");
const todayTotalWeight = ref("");
const totalWeight = ref("");

const router = useRouter();

const searchId = ref("");
const startDate = ref("");
const endDate = ref("");
const loading = ref(false);

const tableData = ref<any[]>([]);
const summaryData = ref<any>({
  weightCountTotal: "0",
  todayTotalQuan: "0"
});

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString();
};

const submit = async () => {
  try {
    if (!message1.value) {
      ElMessage.warning("请输入量化值");
      return;
    }
    
    const params = {
      isEnabled: radio10.value === "1" ? 0 : 1,
      proportion: parseFloat(message1.value)
    };
    
    const response: any = await saveOrUpdateQuantifyCount(params);
    
    if (response.code === 200) {
      ElMessage.success("保存成功");
      await fetchQuantifySettings();
    } else {
      ElMessage.error(response.message || "保存失败");
    }
  } catch (error: any) {
    ElMessage.error(error?.message || error?.response?.data?.message || "保存失败");
  }
};

const filteredData = computed(() => {
  return tableData.value;
});

const exportToExcel = async () => {
  try {
    const params = {
      startTime: startDate.value || undefined,
      endTime: endDate.value || undefined
    };
    
    const token = Session.getToken();
    const response = await axios.post('/mall-project/api/exportQuantifyCountExcel', params, {
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });
    
    const blob = new Blob([response.data], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    });
    
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `量化数数据_${new Date().toISOString().split('T')[0]}.xlsx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  } catch (error: any) {
    ElMessage.error(error?.response?.data?.message || error?.message || "导出失败");
  }
};

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      phone: searchId.value,
      startTime: startDate.value || undefined,
      endTime: endDate.value || undefined,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    };

    const response: any = await QueryQuantifyCountPages(params);

    if (response.code === 200) {
      const data = response.data;
      tableData.value = data.list || [];
      total.value = data.total || 0;
      summaryData.value = data.summary || {
        weightCountTotal: "0",
        todayTotalQuan: "0"
      };

      // 同时更新统计数据，因为新接口同时返回统计信息
      if (data && data.summary) {
        const summary = data.summary;
        // 更新各个统计字段
        message3.value = summary.adminDailyQuantity?.toString() || "0";           // Admin的每日累计量化数
        message4.value = summary.cweightCountTotal?.toString() || "0";            // 中南惠C的每日所有ID累计量化数
        message5.value = summary.bweightCountTotal?.toString() || "0";            // 中南惠B的每日所有ID累计量化数
        message6.value = summary.sumWeightCountTotal?.toString() || "0";          // 中南惠系统的每日总累计量化数
        adminTotalQuantity.value = summary.adminTotalQuantity?.toString() || "0"; // Admin的累计量化数
        cTotalQuantity.value = summary.cweightCountTotalAllDays?.toString() || "0"; // 中南惠C的所有ID累计量化数
        bTotalQuantity.value = summary.bweightCountTotalAlldays?.toString() || "0"; // 中南惠B的所有ID累计量化数
        systemTotalQuantity.value = summary.sumWeightCountTotalAllDays?.toString() || "0"; // 中南惠系统的总累计量化数
        todayTotalWeight.value= summary.todayTotalWeight?.toString() || "0"; // 今日总分量
        totalWeight.value = summary.totalWeight?.toString() || "0"; // 今日累计总分量
      }
    } else {
      ElMessage.error(response.message || "数据加载失败");
    }
  } catch (error: any) {
    ElMessage.error(error?.message || error?.response?.data?.message || "获取数据失败");
  } finally {
    loading.value = false;
  }
};

const fetchQuantifySettings = async () => {
  try {
    const response: any = await getQuantifyCount();
    if (response.code === 200) {
      const data = response.data;
      radio10.value = data.is_enabled === "0" ? "1" : "2";
      message1.value = data.proportion || "";
    } else {
      ElMessage.error(response.message || "获取设置失败");
    }
  } catch (error: any) {
    ElMessage.error(error?.message || error?.response?.data?.message || "获取设置失败");
  }
};

// 重置搜索条件
const resetSearch = () => {
  searchId.value = "";
  startDate.value = "";
  endDate.value = "";
  currentPage.value = 1;
  fetchData();
};



onMounted(() => {
  fetchData();
  fetchQuantifySettings();
});
</script>

<style lang="scss" scoped>
.page-container {
  width: 100%;
  min-height: 100vh;
  background-color: #f0f2f5;
  padding: 24px;
}

.content-wrapper {
  max-width: 1600px;
  margin: 0 auto;
}

.page-header {
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  
  h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: #262626;
  }
}

.card-header {
  display: flex;
  align-items: center;
  height: 24px;
  
  span {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
  }
}

:deep(.el-card) {
  border-radius: 8px;
  margin-bottom: 24px;
  border: none;
  
  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .el-card__body {
    padding: 24px;
  }
}

.setting-card {
  .setting-row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 24px;
    margin-top: 24px;
  }
  
  .setting-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .status-switch {
    margin-right: 16px;
  }
  
  .setting-label {
    margin-right: 12px;
    font-size: 14px;
    color: #262626;
  }
  
  .setting-input {
    width: 220px;
  }
}

.stats-card {
  .stats-rows {
    display: flex;
    flex-direction: column;
  }
  
  .stats-row {
    display: flex;
    justify-content: space-between;
    gap: 24px;
    padding: 16px;
    border-radius: 6px;
    background-color: #fafafa;
    transition: background-color 0.3s ease;
    
    &:hover {
      background-color: #f0f7ff;
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
    }
  }
  
  .stats-divider {
    height: 1px;
    background: linear-gradient(to right, rgba(0,0,0,0.03), rgba(0,0,0,0.1), rgba(0,0,0,0.03));
    margin: 8px 0;
  }
  
  .stats-item {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    
    .stats-label {
      min-width: 200px;
      font-size: 14px;
      color: #595959;
      margin-right: 12px;
      font-weight: 500;
    }
    
    .stats-input {
      width: 180px;
      
      :deep(.el-input__wrapper) {
        background-color: #fff;
        box-shadow: 0 0 0 1px #e0e0e0 inset;
        border-radius: 4px;
      }
      
      :deep(.el-input__inner) {
        color: #1890ff;
        font-weight: 500;
        text-align: center;
      }
    }
  }
}

.table-card {
  .search-form {
    margin-bottom: 24px;

    .form-row {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 24px;
      gap: 24px;
    }
    
    .form-item {
      display: flex;
      align-items: center;
      
      .form-label {
        width: 80px;
        font-size: 14px;
        color: #262626;
        margin-right: 12px;
      }
      
      .el-input, .el-date-picker {
        width: 240px;
      }
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-start;
      gap: 16px;

      .el-button {
        min-width: 100px; // 设置最小宽度防止loading时宽度变化

        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }

  .summary-section {
    display: flex;
    gap: 80px;
    margin-bottom: 24px;
    font-size: 14px;
    align-items: center;
  }

  .summary-item {
    display: flex;
    align-items: center;

    .summary-label {
      color: #595959;
      margin-right: 12px;
      font-size: 14px;
      white-space: nowrap;
    }

    .summary-input {
      width: 180px;

      :deep(.el-input__wrapper) {
        background-color: #f5f5f5;
        box-shadow: 0 0 0 1px #e0e0e0 inset;
        border-radius: 4px;
      }

      :deep(.el-input__inner) {
        color: #1890ff;
        font-weight: 500;
        text-align: center;
      }
    }
  }

  :deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
    
    th.el-table__cell {
      background-color: #fafafa;
      color: #262626;
      font-weight: 500;
    }
    
    .cell {
      padding: 12px 16px;
    }
  }
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: flex-start;
}

:deep(.el-input .el-input__prefix) {
  color: #a0a0a0;
}
</style>